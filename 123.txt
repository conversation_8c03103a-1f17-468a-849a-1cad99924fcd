[{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "UploadProgress redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 19,
	"startColumn": 6,
	"endLineNumber": 19,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 18,
			"startColumn": 6,
			"endLineNumber": 18,
			"endColumn": 20,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "UploadProgress redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 19,
	"startColumn": 6,
	"endLineNumber": 19,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 20,
			"startColumn": 6,
			"endLineNumber": 20,
			"endColumn": 20,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "ChunkUploadTask redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 28,
	"startColumn": 6,
	"endLineNumber": 28,
	"endColumn": 21,
	"relatedInformation": [
		{
			"startLineNumber": 39,
			"startColumn": 6,
			"endLineNumber": 39,
			"endColumn": 21,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "main redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 34,
	"startColumn": 6,
	"endLineNumber": 34,
	"endColumn": 10,
	"relatedInformation": [
		{
			"startLineNumber": 25,
			"startColumn": 6,
			"endLineNumber": 25,
			"endColumn": 10,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "main redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 34,
	"startColumn": 6,
	"endLineNumber": 34,
	"endColumn": 10,
	"relatedInformation": [
		{
			"startLineNumber": 16,
			"startColumn": 6,
			"endLineNumber": 16,
			"endColumn": 10,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/simple_processor.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "main redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 34,
	"startColumn": 6,
	"endLineNumber": 34,
	"endColumn": 10,
	"relatedInformation": [
		{
			"startLineNumber": 46,
			"startColumn": 6,
			"endLineNumber": 46,
			"endColumn": 10,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "scanChunkFiles redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 216,
	"startColumn": 6,
	"endLineNumber": 216,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 311,
			"startColumn": 6,
			"endLineNumber": 311,
			"endColumn": 20,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "scanChunkFiles redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 216,
	"startColumn": 6,
	"endLineNumber": 216,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 135,
			"startColumn": 6,
			"endLineNumber": 135,
			"endColumn": 20,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "loadProgress redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 236,
	"startColumn": 6,
	"endLineNumber": 236,
	"endColumn": 18,
	"relatedInformation": [
		{
			"startLineNumber": 155,
			"startColumn": 6,
			"endLineNumber": 155,
			"endColumn": 18,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "loadProgress redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 236,
	"startColumn": 6,
	"endLineNumber": 236,
	"endColumn": 18,
	"relatedInformation": [
		{
			"startLineNumber": 326,
			"startColumn": 6,
			"endLineNumber": 326,
			"endColumn": 18,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "saveProgress redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 254,
	"startColumn": 6,
	"endLineNumber": 254,
	"endColumn": 18,
	"relatedInformation": [
		{
			"startLineNumber": 172,
			"startColumn": 6,
			"endLineNumber": 172,
			"endColumn": 18,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "saveProgress redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 254,
	"startColumn": 6,
	"endLineNumber": 254,
	"endColumn": 18,
	"relatedInformation": [
		{
			"startLineNumber": 341,
			"startColumn": 6,
			"endLineNumber": 341,
			"endColumn": 18,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "generateFinalResult redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 263,
	"startColumn": 6,
	"endLineNumber": 263,
	"endColumn": 25,
	"relatedInformation": [
		{
			"startLineNumber": 348,
			"startColumn": 6,
			"endLineNumber": 348,
			"endColumn": 25,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "generateFinalResult redeclared in this block (see details)",
	"source": "compiler",
	"startLineNumber": 263,
	"startColumn": 6,
	"endLineNumber": 263,
	"endColumn": 25,
	"relatedInformation": [
		{
			"startLineNumber": 178,
			"startColumn": 6,
			"endLineNumber": 178,
			"endColumn": 25,
			"message": "",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go"
		}
	]
}][{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "UploadProgress redeclared in this block",
	"source": "compiler",
	"startLineNumber": 18,
	"startColumn": 6,
	"endLineNumber": 18,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 19,
			"startColumn": 6,
			"endLineNumber": 19,
			"endColumn": 20,
			"message": "other declaration of UploadProgress",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	],
	"modelVersionId": 4
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "main redeclared in this block",
	"source": "compiler",
	"startLineNumber": 25,
	"startColumn": 6,
	"endLineNumber": 25,
	"endColumn": 10,
	"relatedInformation": [
		{
			"startLineNumber": 34,
			"startColumn": 6,
			"endLineNumber": 34,
			"endColumn": 10,
			"message": "other declaration of main",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	],
	"modelVersionId": 4
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "scanChunkFiles redeclared in this block",
	"source": "compiler",
	"startLineNumber": 135,
	"startColumn": 6,
	"endLineNumber": 135,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 216,
			"startColumn": 6,
			"endLineNumber": 216,
			"endColumn": 20,
			"message": "other declaration of scanChunkFiles",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	],
	"modelVersionId": 4
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "loadProgress redeclared in this block",
	"source": "compiler",
	"startLineNumber": 155,
	"startColumn": 6,
	"endLineNumber": 155,
	"endColumn": 18,
	"relatedInformation": [
		{
			"startLineNumber": 236,
			"startColumn": 6,
			"endLineNumber": 236,
			"endColumn": 18,
			"message": "other declaration of loadProgress",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	],
	"modelVersionId": 4
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "saveProgress redeclared in this block",
	"source": "compiler",
	"startLineNumber": 172,
	"startColumn": 6,
	"endLineNumber": 172,
	"endColumn": 18,
	"relatedInformation": [
		{
			"startLineNumber": 254,
			"startColumn": 6,
			"endLineNumber": 254,
			"endColumn": 18,
			"message": "other declaration of saveProgress",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	],
	"modelVersionId": 4
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/resume_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "generateFinalResult redeclared in this block",
	"source": "compiler",
	"startLineNumber": 178,
	"startColumn": 6,
	"endLineNumber": 178,
	"endColumn": 25,
	"relatedInformation": [
		{
			"startLineNumber": 263,
			"startColumn": 6,
			"endLineNumber": 263,
			"endColumn": 25,
			"message": "other declaration of generateFinalResult",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	],
	"modelVersionId": 4
}][{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/simple_processor.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "main redeclared in this block",
	"source": "compiler",
	"startLineNumber": 16,
	"startColumn": 6,
	"endLineNumber": 16,
	"endColumn": 10,
	"relatedInformation": [
		{
			"startLineNumber": 34,
			"startColumn": 6,
			"endLineNumber": 34,
			"endColumn": 10,
			"message": "other declaration of main",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	]
}][{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UnusedImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UnusedImport"
		}
	},
	"severity": 8,
	"message": "\"magnet-downloader/pkg/mixfile\" imported and not used",
	"source": "compiler",
	"startLineNumber": 16,
	"startColumn": 2,
	"endLineNumber": 16,
	"endColumn": 33,
	"tags": [
		1
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "UploadProgress redeclared in this block",
	"source": "compiler",
	"startLineNumber": 20,
	"startColumn": 6,
	"endLineNumber": 20,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 19,
			"startColumn": 6,
			"endLineNumber": 19,
			"endColumn": 20,
			"message": "other declaration of UploadProgress",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "ChunkUploadTask redeclared in this block",
	"source": "compiler",
	"startLineNumber": 39,
	"startColumn": 6,
	"endLineNumber": 39,
	"endColumn": 21,
	"relatedInformation": [
		{
			"startLineNumber": 28,
			"startColumn": 6,
			"endLineNumber": 28,
			"endColumn": 21,
			"message": "other declaration of ChunkUploadTask",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "main redeclared in this block",
	"source": "compiler",
	"startLineNumber": 46,
	"startColumn": 6,
	"endLineNumber": 46,
	"endColumn": 10,
	"relatedInformation": [
		{
			"startLineNumber": 34,
			"startColumn": 6,
			"endLineNumber": 34,
			"endColumn": 10,
			"message": "other declaration of main",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.LoadInt64",
	"source": "compiler",
	"startLineNumber": 106,
	"startColumn": 20,
	"endLineNumber": 106,
	"endColumn": 44
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.LoadInt64",
	"source": "compiler",
	"startLineNumber": 107,
	"startColumn": 28,
	"endLineNumber": 107,
	"endColumn": 52
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.LoadInt64",
	"source": "compiler",
	"startLineNumber": 109,
	"startColumn": 26,
	"endLineNumber": 109,
	"endColumn": 50
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field Size in struct literal of type ChunkUploadTask",
	"source": "compiler",
	"startLineNumber": 130,
	"startColumn": 5,
	"endLineNumber": 130,
	"endColumn": 9
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.LoadInt64",
	"source": "compiler",
	"startLineNumber": 163,
	"startColumn": 26,
	"endLineNumber": 163,
	"endColumn": 50
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.LoadInt64",
	"source": "compiler",
	"startLineNumber": 167,
	"startColumn": 52,
	"endLineNumber": 167,
	"endColumn": 76
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.LoadInt64",
	"source": "compiler",
	"startLineNumber": 222,
	"startColumn": 32,
	"endLineNumber": 222,
	"endColumn": 56
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.LoadInt64",
	"source": "compiler",
	"startLineNumber": 255,
	"startColumn": 32,
	"endLineNumber": 255,
	"endColumn": 56
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.AddInt64",
	"source": "compiler",
	"startLineNumber": 305,
	"startColumn": 18,
	"endLineNumber": 305,
	"endColumn": 42
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "task.Size undefined (type ChunkUploadTask has no field or method Size)",
	"source": "compiler",
	"startLineNumber": 307,
	"startColumn": 45,
	"endLineNumber": 307,
	"endColumn": 49
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "scanChunkFiles redeclared in this block",
	"source": "compiler",
	"startLineNumber": 311,
	"startColumn": 6,
	"endLineNumber": 311,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 216,
			"startColumn": 6,
			"endLineNumber": 216,
			"endColumn": 20,
			"message": "other declaration of scanChunkFiles",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "loadProgress redeclared in this block",
	"source": "compiler",
	"startLineNumber": 326,
	"startColumn": 6,
	"endLineNumber": 326,
	"endColumn": 18,
	"relatedInformation": [
		{
			"startLineNumber": 236,
			"startColumn": 6,
			"endLineNumber": 236,
			"endColumn": 18,
			"message": "other declaration of loadProgress",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &progress.UploadedChunks (value of type *int) as *int64 value in argument to atomic.StoreInt64",
	"source": "compiler",
	"startLineNumber": 336,
	"startColumn": 21,
	"endLineNumber": 336,
	"endColumn": 45
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "saveProgress redeclared in this block",
	"source": "compiler",
	"startLineNumber": 341,
	"startColumn": 6,
	"endLineNumber": 341,
	"endColumn": 18,
	"relatedInformation": [
		{
			"startLineNumber": 254,
			"startColumn": 6,
			"endLineNumber": 254,
			"endColumn": 18,
			"message": "other declaration of saveProgress",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/cmd/turbo_upload.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "generateFinalResult redeclared in this block",
	"source": "compiler",
	"startLineNumber": 348,
	"startColumn": 6,
	"endLineNumber": 348,
	"endColumn": 25,
	"relatedInformation": [
		{
			"startLineNumber": 263,
			"startColumn": 6,
			"endLineNumber": 263,
			"endColumn": 25,
			"message": "other declaration of generateFinalResult",
			"resource": "/www/wwwroot/JAVAPI.COM/cmd/concurrent_upload.go"
		}
	]
}][{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 78,
	"startColumn": 3,
	"endLineNumber": 78,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UnusedVar",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UnusedVar"
		}
	},
	"severity": 8,
	"message": "declared and not used: shareCode",
	"source": "compiler",
	"startLineNumber": 83,
	"startColumn": 2,
	"endLineNumber": 83,
	"endColumn": 11,
	"tags": [
		1
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 85,
	"startColumn": 3,
	"endLineNumber": 85,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 92,
	"startColumn": 3,
	"endLineNumber": 92,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 108,
	"startColumn": 2,
	"endLineNumber": 108,
	"endColumn": 10
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 115,
	"startColumn": 3,
	"endLineNumber": 115,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 122,
	"startColumn": 3,
	"endLineNumber": 122,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 138,
	"startColumn": 2,
	"endLineNumber": 138,
	"endColumn": 10
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 145,
	"startColumn": 3,
	"endLineNumber": 145,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 152,
	"startColumn": 3,
	"endLineNumber": 152,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 165,
	"startColumn": 3,
	"endLineNumber": 165,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 259,
	"startColumn": 3,
	"endLineNumber": 259,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 266,
	"startColumn": 3,
	"endLineNumber": 266,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 277,
	"startColumn": 4,
	"endLineNumber": 277,
	"endColumn": 12
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 289,
	"startColumn": 3,
	"endLineNumber": 289,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 295,
	"startColumn": 3,
	"endLineNumber": 295,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 299,
	"startColumn": 2,
	"endLineNumber": 299,
	"endColumn": 10
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 309,
	"startColumn": 2,
	"endLineNumber": 309,
	"endColumn": 10
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 315,
	"startColumn": 2,
	"endLineNumber": 315,
	"endColumn": 10
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 322,
	"startColumn": 2,
	"endLineNumber": 322,
	"endColumn": 10
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 329,
	"startColumn": 3,
	"endLineNumber": 329,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 336,
	"startColumn": 3,
	"endLineNumber": 336,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 342,
	"startColumn": 3,
	"endLineNumber": 342,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 347,
	"startColumn": 3,
	"endLineNumber": 347,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 353,
	"startColumn": 3,
	"endLineNumber": 353,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/internal/api/handler/mixfile_handler.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: response",
	"source": "compiler",
	"startLineNumber": 357,
	"startColumn": 2,
	"endLineNumber": 357,
	"endColumn": 10
}][{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "UnusedImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UnusedImport"
		}
	},
	"severity": 8,
	"message": "\"magnet-downloader/pkg/aria2\" imported and not used",
	"source": "compiler",
	"startLineNumber": 12,
	"startColumn": 2,
	"endLineNumber": 12,
	"endColumn": 31,
	"tags": [
		1
	]
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import gorm.io/driver/sqlite (no required module provides package \"gorm.io/driver/sqlite\")",
	"source": "compiler",
	"startLineNumber": 18,
	"startColumn": 2,
	"endLineNumber": 18,
	"endColumn": 25
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field MockMode in struct literal of type imgbb.Config",
	"source": "compiler",
	"startLineNumber": 124,
	"startColumn": 3,
	"endLineNumber": 124,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field SegmentDuration in struct literal of type streaming.PlaylistConfig",
	"source": "compiler",
	"startLineNumber": 129,
	"startColumn": 3,
	"endLineNumber": 129,
	"endColumn": 18
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field OutputDir in struct literal of type streaming.PlaylistConfig",
	"source": "compiler",
	"startLineNumber": 130,
	"startColumn": 3,
	"endLineNumber": 130,
	"endColumn": 12
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "WrongArgCount",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "WrongArgCount"
		}
	},
	"severity": 8,
	"message": "not enough arguments in call to service.NewFileProcessingService\n\thave (*gorm.DB, *MockWebSocketService, *fileprocessor.ProcessingConfig, *imgbb.Config, *streaming.PlaylistConfig)\n\twant (*gorm.DB, *\"magnet-downloader/internal/websocket\".Service, *fileprocessor.ProcessingConfig, *imgbb.Config, *streaming.PlaylistConfig, *config.MixFileConfig)",
	"source": "compiler",
	"startLineNumber": 142,
	"startColumn": 2,
	"endLineNumber": 142,
	"endColumn": 2
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "fileProcessingService.GetActualFilePath undefined (type service.FileProcessingService has no field or method GetActualFilePath)",
	"source": "compiler",
	"startLineNumber": 145,
	"startColumn": 47,
	"endLineNumber": 145,
	"endColumn": 64
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "fileProcessingService.ValidateFilePath undefined (type service.FileProcessingService has no field or method ValidateFilePath)",
	"source": "compiler",
	"startLineNumber": 157,
	"startColumn": 34,
	"endLineNumber": 157,
	"endColumn": 50
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field MockMode in struct literal of type imgbb.Config",
	"source": "compiler",
	"startLineNumber": 222,
	"startColumn": 3,
	"endLineNumber": 222,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field SegmentDuration in struct literal of type streaming.PlaylistConfig",
	"source": "compiler",
	"startLineNumber": 226,
	"startColumn": 3,
	"endLineNumber": 226,
	"endColumn": 18
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field OutputDir in struct literal of type streaming.PlaylistConfig",
	"source": "compiler",
	"startLineNumber": 227,
	"startColumn": 3,
	"endLineNumber": 227,
	"endColumn": 12
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "WrongArgCount",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "WrongArgCount"
		}
	},
	"severity": 8,
	"message": "not enough arguments in call to service.NewFileProcessingService\n\thave (*gorm.DB, *MockWebSocketService, *fileprocessor.ProcessingConfig, *imgbb.Config, *streaming.PlaylistConfig)\n\twant (*gorm.DB, *\"magnet-downloader/internal/websocket\".Service, *fileprocessor.ProcessingConfig, *imgbb.Config, *streaming.PlaylistConfig, *config.MixFileConfig)",
	"source": "compiler",
	"startLineNumber": 238,
	"startColumn": 2,
	"endLineNumber": 238,
	"endColumn": 2
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "fileProcessingService.GetActualFilePath undefined (type service.FileProcessingService has no field or method GetActualFilePath)",
	"source": "compiler",
	"startLineNumber": 241,
	"startColumn": 45,
	"endLineNumber": 241,
	"endColumn": 62
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field MockMode in struct literal of type imgbb.Config",
	"source": "compiler",
	"startLineNumber": 278,
	"startColumn": 3,
	"endLineNumber": 278,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field SegmentDuration in struct literal of type streaming.PlaylistConfig",
	"source": "compiler",
	"startLineNumber": 282,
	"startColumn": 3,
	"endLineNumber": 282,
	"endColumn": 18
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field OutputDir in struct literal of type streaming.PlaylistConfig",
	"source": "compiler",
	"startLineNumber": 283,
	"startColumn": 3,
	"endLineNumber": 283,
	"endColumn": 12
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "WrongArgCount",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "WrongArgCount"
		}
	},
	"severity": 8,
	"message": "not enough arguments in call to service.NewFileProcessingService\n\thave (*gorm.DB, *MockWebSocketService, *fileprocessor.ProcessingConfig, *imgbb.Config, *streaming.PlaylistConfig)\n\twant (*gorm.DB, *\"magnet-downloader/internal/websocket\".Service, *fileprocessor.ProcessingConfig, *imgbb.Config, *streaming.PlaylistConfig, *config.MixFileConfig)",
	"source": "compiler",
	"startLineNumber": 294,
	"startColumn": 2,
	"endLineNumber": 294,
	"endColumn": 2
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field MockMode in struct literal of type imgbb.Config",
	"source": "compiler",
	"startLineNumber": 357,
	"startColumn": 3,
	"endLineNumber": 357,
	"endColumn": 11
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field SegmentDuration in struct literal of type streaming.PlaylistConfig",
	"source": "compiler",
	"startLineNumber": 361,
	"startColumn": 3,
	"endLineNumber": 361,
	"endColumn": 18
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field OutputDir in struct literal of type streaming.PlaylistConfig",
	"source": "compiler",
	"startLineNumber": 362,
	"startColumn": 3,
	"endLineNumber": 362,
	"endColumn": 12
},{
	"resource": "/www/wwwroot/JAVAPI.COM/tests/integration/file_processing_test.go",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "WrongArgCount",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "WrongArgCount"
		}
	},
	"severity": 8,
	"message": "not enough arguments in call to service.NewFileProcessingService\n\thave (*gorm.DB, *MockWebSocketService, *fileprocessor.ProcessingConfig, *imgbb.Config, *streaming.PlaylistConfig)\n\twant (*gorm.DB, *\"magnet-downloader/internal/websocket\".Service, *fileprocessor.ProcessingConfig, *imgbb.Config, *streaming.PlaylistConfig, *config.MixFileConfig)",
	"source": "compiler",
	"startLineNumber": 373,
	"startColumn": 2,
	"endLineNumber": 373,
	"endColumn": 2
}]