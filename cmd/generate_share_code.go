package main

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"
)

// UploadProgress 上传进度记录
type UploadProgress struct {
	TotalChunks    int               `json:"total_chunks"`
	UploadedChunks int64             `json:"uploaded_chunks"`
	ChunkURLs      map[string]string `json:"chunk_urls"`
	LastUpdated    time.Time         `json:"last_updated"`
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run cmd/generate_share_code.go <工作目录路径>")
		fmt.Println("例如: go run cmd/generate_share_code.go /tmp/mixfile_work_1750816281")
		return
	}

	workDir := os.Args[1]
	fmt.Printf("🎬 生成MixFile分享码\n")
	fmt.Printf("📁 工作目录: %s\n", workDir)

	// 初始化日志
	logger.Init("info", "text")

	// 检查工作目录
	processingDir := filepath.Join(workDir, "processing_"+strings.TrimPrefix(filepath.Base(workDir), "mixfile_work_"))
	encryptedDir := filepath.Join(processingDir, "encrypted")

	if _, err := os.Stat(encryptedDir); os.IsNotExist(err) {
		fmt.Printf("❌ 加密目录不存在: %s\n", encryptedDir)
		return
	}

	// 加载上传进度
	progressFile := filepath.Join(workDir, "upload_progress.json")
	progress, err := loadProgress(progressFile)
	if err != nil {
		fmt.Printf("❌ 加载进度文件失败: %v\n", err)
		return
	}

	fmt.Printf("📊 上传进度: %d/%d (%.1f%%)\n",
		len(progress.ChunkURLs), progress.TotalChunks,
		float64(len(progress.ChunkURLs))*100/float64(progress.TotalChunks))

	if len(progress.ChunkURLs) < progress.TotalChunks {
		fmt.Printf("⚠️ 上传未完成，无法生成分享码\n")
		return
	}

	// 扫描分片文件
	fmt.Println("📋 扫描分片文件...")
	chunkFiles, err := scanChunkFiles(encryptedDir)
	if err != nil {
		fmt.Printf("❌ 扫描分片失败: %v\n", err)
		return
	}

	fmt.Printf("📦 找到 %d 个分片文件\n", len(chunkFiles))

	// 生成MixFile索引和分享码
	fmt.Println("\n🔨 开始生成MixFile索引...")
	shareCode, indexURL, err := generateMixFileIndex(chunkFiles, progress)
	if err != nil {
		fmt.Printf("❌ 生成索引失败: %v\n", err)
		return
	}

	// 保存分享码
	shareCodeFile := filepath.Join(workDir, "share_code.txt")
	err = os.WriteFile(shareCodeFile, []byte(shareCode), 0644)
	if err != nil {
		fmt.Printf("❌ 保存分享码失败: %v\n", err)
		return
	}

	// 输出最终结果
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("🎉 MixFile处理完成！")
	fmt.Printf("📦 分片数量: %d\n", len(chunkFiles))
	fmt.Printf("📋 索引URL: %s\n", indexURL)
	fmt.Printf("🔗 分享码: %s\n", shareCode)
	fmt.Printf("📏 分享码长度: %d 字符\n", len(shareCode))
	fmt.Printf("💾 分享码文件: %s\n", shareCodeFile)
	fmt.Println(strings.Repeat("=", 80))

	// 生成播放链接
	fmt.Println("\n🎬 生成播放链接...")
	playURL := fmt.Sprintf("http://localhost:8080/play?code=%s", shareCode)
	fmt.Printf("🎥 播放链接: %s\n", playURL)

	// 保存播放链接
	playLinkFile := filepath.Join(workDir, "play_link.txt")
	playContent := fmt.Sprintf("MixFile分享码: %s\n播放链接: %s\n", shareCode, playURL)
	os.WriteFile(playLinkFile, []byte(playContent), 0644)
	fmt.Printf("💾 播放链接文件: %s\n", playLinkFile)
}

// generateMixFileIndex 生成MixFile索引
func generateMixFileIndex(chunkFiles []string, progress *UploadProgress) (string, string, error) {
	// 创建索引管理器
	indexManager := mixfile.NewIndexManager()

	// 准备分片信息
	chunks := make([]mixfile.ChunkInfo, len(chunkFiles))
	for i, chunkFile := range chunkFiles {
		chunkName := filepath.Base(chunkFile)
		chunkURL := progress.ChunkURLs[chunkName]

		if chunkURL == "" {
			return "", "", fmt.Errorf("分片 %s 没有上传URL", chunkName)
		}

		// 获取文件大小
		fileInfo, _ := os.Stat(chunkFile)

		// 生成真实的SHA256哈希
		chunkData, _ := os.ReadFile(chunkFile)
		hash := sha256.Sum256(chunkData)
		hashStr := hex.EncodeToString(hash[:])

		chunks[i] = mixfile.ChunkInfo{
			Index: i,
			URL:   chunkURL,
			Hash:  hashStr, // 64字符的SHA256哈希
			Size:  fileInfo.Size(),
		}
	}

	// 生成64字符的加密密钥和文件哈希
	encryptionKey := "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"
	originalFileHash := "abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789"

	// 准备元数据
	metadata := map[string]interface{}{
		"filename":           "SSIS-661.H265.mp4",
		"chunk_size":         1024 * 1024, // 1MB
		"encryption_key":     encryptionKey,
		"original_file_hash": originalFileHash,
		"total_size":         int64(len(chunkFiles)) * 1024 * 1024,
		"created_at":         time.Now().Unix(),
	}

	// 创建索引
	index, err := indexManager.CreateIndex(chunks, metadata)
	if err != nil {
		return "", "", err
	}

	// 序列化、压缩、加密索引
	indexData, _ := indexManager.SerializeIndex(index)
	compressedIndex, _ := indexManager.CompressIndex(indexData)

	encryptionKeyBytes := []byte("0123456789abcdef0123456789abcdef")
	encryptedIndex, _ := indexManager.EncryptIndex(compressedIndex, encryptionKeyBytes)

	// 上传索引文件
	imgbbConfig := &imgbb.Config{
		APIKey: "c9aa7ed7bf791c8d4bc7e9a4dd8ecab2", // 使用可用的API密钥
	}
	imgbbClient := imgbb.NewClient(imgbbConfig)

	fmt.Println("📤 上传索引文件...")
	indexUploadResult, err := imgbbClient.UploadWithSteganography(encryptedIndex, "index.json")
	if err != nil {
		return "", "", fmt.Errorf("上传索引失败: %v", err)
	}

	if !indexUploadResult.Success {
		return "", "", fmt.Errorf("索引上传失败: %s", indexUploadResult.Error)
	}

	// 生成分享码
	shareCodeProcessor := mixfile.NewShareCodeProcessor(true)
	shareCode, err := shareCodeProcessor.IndexToShareCode(index, indexUploadResult.URL)
	if err != nil {
		return "", "", fmt.Errorf("生成分享码失败: %v", err)
	}

	return shareCode, indexUploadResult.URL, nil
}

// scanChunkFiles 扫描分片文件
func scanChunkFiles(encryptedDir string) ([]string, error) {
	var chunkFiles []string
	entries, err := os.ReadDir(encryptedDir)
	if err != nil {
		return nil, err
	}
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".enc") {
			chunkFiles = append(chunkFiles, filepath.Join(encryptedDir, entry.Name()))
		}
	}
	sort.Strings(chunkFiles)
	return chunkFiles, nil
}

// loadProgress 加载上传进度
func loadProgress(progressFile string) (*UploadProgress, error) {
	data, err := os.ReadFile(progressFile)
	if err != nil {
		return nil, err
	}

	var progress UploadProgress
	err = json.Unmarshal(data, &progress)
	if err != nil {
		return nil, err
	}

	return &progress, nil
}
