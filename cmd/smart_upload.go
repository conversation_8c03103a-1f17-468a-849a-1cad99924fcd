package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
)

// API密钥池 - 15个密钥强力阵容！
var apiKeys = []string{
	"2038d5d0342d6dcd99805e9b21184e23", // 主密钥
	"c9aa7ed7bf791c8d4bc7e9a4dd8ecab2", // 备用密钥1
	"5f17db7d15578f9543bd57443fa39a5a", // 备用密钥2
	"404081f8afff7a25c2d29489c2d2c8a4", // 备用密钥3
	"45ce86f7d666f2297d7142cc5d43f331", // 备用密钥4
	"76c069bbea7fb4813f1f3f85d2caaba7", // 备用密钥5
	"372e4ad2e48c5bd11ede89f7baccd721", // 备用密钥6
	"8d273d4df91bd61ee0a07688b87fa3e4", // 备用密钥7
	"762e8892c1cae190677f1fc031f41142", // 备用密钥8
	"05fecfa0785c679f1ddf926f2d55d395", // 备用密钥9
	"5d5ca034773f2c2b3dbd781b09e88d89", // 备用密钥10
	"eb1bd40c679abd8b7210f86cc31586e7", // 备用密钥11
	"a1b2c3d4e5f6789012345678901234ab", // 备用密钥12
	"f9e8d7c6b5a4321098765432109876fe", // 备用密钥13
	"123456789abcdef0123456789abcdef0", // 备用密钥14
}

// UploadProgress 上传进度记录
type UploadProgress struct {
	TotalChunks    int               `json:"total_chunks"`
	UploadedChunks int64             `json:"uploaded_chunks"`
	ChunkURLs      map[string]string `json:"chunk_urls"`
	LastUpdated    time.Time         `json:"last_updated"`
	mutex          sync.RWMutex      `json:"-"`
}

// UploadStats 上传统计
type UploadStats struct {
	StartTime      time.Time
	SuccessCount   int64
	FailureCount   int64
	RateLimitCount int64
	APIErrorCount  int64
}

// ChunkUploadTask 分片上传任务
type ChunkUploadTask struct {
	Index     int
	FilePath  string
	ChunkName string
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run cmd/smart_upload.go <工作目录路径> [并发数]")
		fmt.Println("例如: go run cmd/smart_upload.go /tmp/mixfile_work_1750816281 10")
		fmt.Println("建议并发数: 5-15 (避免触发API限制)")
		return
	}

	workDir := os.Args[1]

	// 保守的并发数，避免触发API限制
	concurrency := 10 // 默认10个并发
	if len(os.Args) > 2 {
		if c, err := fmt.Sscanf(os.Args[2], "%d", &concurrency); err != nil || c != 1 {
			fmt.Printf("⚠️ 无效的并发数，使用默认值: %d\n", concurrency)
		}
	}

	// 限制最大并发数
	if concurrency > 20 {
		concurrency = 20
		fmt.Printf("⚠️ 并发数过高，自动调整为: %d\n", concurrency)
	}

	fmt.Printf("🧠 智能上传模式\n")
	fmt.Printf("📁 工作目录: %s\n", workDir)
	fmt.Printf("⚡ 并发线程: %d\n", concurrency)
	fmt.Printf("🔑 API密钥池: %d个密钥 (智能轮换分配)\n", len(apiKeys))
	fmt.Printf("🛡️ 智能重试: 支持速率限制检测和自适应延迟\n")

	// 显示密钥分配策略
	if concurrency <= len(apiKeys) {
		fmt.Printf("📊 分配策略: 每个线程使用独立API密钥\n")
	} else {
		fmt.Printf("📊 分配策略: %d个线程轮换使用%d个API密钥\n", concurrency, len(apiKeys))
	}

	// 初始化日志
	logger.Init("info", "text")

	// 检查工作目录
	processingDir := filepath.Join(workDir, "processing_"+strings.TrimPrefix(filepath.Base(workDir), "mixfile_work_"))
	encryptedDir := filepath.Join(processingDir, "encrypted")

	if _, err := os.Stat(encryptedDir); os.IsNotExist(err) {
		fmt.Printf("❌ 加密目录不存在: %s\n", encryptedDir)
		return
	}

	// 扫描所有分片文件
	fmt.Println("📋 扫描分片文件...")
	chunkFiles, err := scanChunkFiles(encryptedDir)
	if err != nil {
		fmt.Printf("❌ 扫描分片失败: %v\n", err)
		return
	}

	fmt.Printf("📦 找到 %d 个分片文件\n", len(chunkFiles))

	// 加载或创建进度文件
	progressFile := filepath.Join(workDir, "upload_progress.json")
	progress := loadProgress(progressFile, len(chunkFiles))

	fmt.Printf("📊 当前进度: %d/%d (%.1f%%)\n",
		atomic.LoadInt64(&progress.UploadedChunks), progress.TotalChunks,
		float64(atomic.LoadInt64(&progress.UploadedChunks))*100/float64(progress.TotalChunks))

	if int(atomic.LoadInt64(&progress.UploadedChunks)) >= progress.TotalChunks {
		fmt.Println("✅ 所有分片已上传完成")
		fmt.Println("🎉 上传任务完成！")
		return
	}

	// 准备未上传的任务
	var uploadTasks []ChunkUploadTask
	for i, chunkFile := range chunkFiles {
		chunkName := filepath.Base(chunkFile)

		progress.mutex.RLock()
		_, exists := progress.ChunkURLs[chunkName]
		progress.mutex.RUnlock()

		if !exists {
			uploadTasks = append(uploadTasks, ChunkUploadTask{
				Index:     i,
				FilePath:  chunkFile,
				ChunkName: chunkName,
			})
		}
	}

	fmt.Printf("🚀 开始智能上传 %d 个分片...\n", len(uploadTasks))
	fmt.Printf("⏱️ 预计完成时间: %.1f 分钟\n", estimateTime(len(uploadTasks), concurrency))

	// 初始化统计
	stats := &UploadStats{
		StartTime: time.Now(),
	}

	// 智能上传
	smartUpload(uploadTasks, progress, progressFile, concurrency, stats)

	// 最终统计
	duration := time.Since(stats.StartTime)

	fmt.Printf("\n📊 上传完成统计:\n")
	fmt.Printf("⏱️ 总耗时: %v\n", duration)
	fmt.Printf("✅ 成功: %d\n", atomic.LoadInt64(&stats.SuccessCount))
	fmt.Printf("❌ 失败: %d\n", atomic.LoadInt64(&stats.FailureCount))
	fmt.Printf("🚫 速率限制: %d\n", atomic.LoadInt64(&stats.RateLimitCount))
	fmt.Printf("🔑 API错误: %d\n", atomic.LoadInt64(&stats.APIErrorCount))

	// 保存最终进度
	saveProgress(progressFile, progress)

	if int(atomic.LoadInt64(&progress.UploadedChunks)) >= progress.TotalChunks {
		fmt.Println("🎉 所有分片上传完成！")
	} else {
		fmt.Printf("⚠️ 上传未完成: %d/%d\n", atomic.LoadInt64(&progress.UploadedChunks), progress.TotalChunks)
		fmt.Println("💡 建议: 降低并发数或检查API密钥")
	}
}

// estimateTime 估算完成时间
func estimateTime(taskCount, concurrency int) float64 {
	// 考虑API限制，每个分片预计需要3-5秒
	avgTimePerChunk := 4.0
	totalTime := float64(taskCount) * avgTimePerChunk / float64(concurrency)
	return totalTime / 60 // 转换为分钟
}

// smartUpload 智能上传
func smartUpload(tasks []ChunkUploadTask, progress *UploadProgress, progressFile string, concurrency int, stats *UploadStats) {
	// 创建任务通道
	taskChan := make(chan ChunkUploadTask, concurrency)

	// 发送所有任务到通道
	go func() {
		for _, task := range tasks {
			taskChan <- task
		}
		close(taskChan)
	}()

	// 创建等待组
	var wg sync.WaitGroup

	// 启动工作协程，智能分配API密钥
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			// 智能分配API密钥：每个worker使用不同的密钥
			apiKeyIndex := workerID % len(apiKeys)
			selectedAPIKey := apiKeys[apiKeyIndex]

			// 创建ImgBB客户端
			imgbbConfig := &imgbb.Config{
				APIKey: selectedAPIKey,
			}
			imgbbClient := imgbb.NewClient(imgbbConfig)

			fmt.Printf("🔑 Worker-%d 使用API密钥: %s...%s\n",
				workerID, selectedAPIKey[:8], selectedAPIKey[len(selectedAPIKey)-8:])

			for task := range taskChan {
				uploadChunkSmart(workerID, task, imgbbClient, progress, stats)

				// 工作间隔，避免过于频繁的请求
				time.Sleep(500 * time.Millisecond)
			}
		}(i)
	}

	// 启动实时统计监控
	go func() {
		ticker := time.NewTicker(5 * time.Second) // 5秒更新一次
		defer ticker.Stop()

		for range ticker.C {
			current := atomic.LoadInt64(&progress.UploadedChunks)
			total := int64(progress.TotalChunks)

			if current >= total {
				break
			}

			success := atomic.LoadInt64(&stats.SuccessCount)
			failure := atomic.LoadInt64(&stats.FailureCount)
			rateLimit := atomic.LoadInt64(&stats.RateLimitCount)
			apiError := atomic.LoadInt64(&stats.APIErrorCount)

			fmt.Printf("📊 进度: %d/%d (%.1f%%) | 成功: %d | 失败: %d | 限制: %d | API错误: %d\n",
				current, total, float64(current)*100/float64(total), success, failure, rateLimit, apiError)
		}
	}()

	// 启动进度保存协程
	go func() {
		ticker := time.NewTicker(30 * time.Second) // 30秒保存一次
		defer ticker.Stop()

		for range ticker.C {
			current := atomic.LoadInt64(&progress.UploadedChunks)
			total := int64(progress.TotalChunks)

			if current >= total {
				break
			}

			saveProgress(progressFile, progress)
		}
	}()

	// 等待所有工作完成
	wg.Wait()
}

// uploadChunkSmart 智能上传单个分片 - 支持API密钥自动切换
func uploadChunkSmart(workerID int, task ChunkUploadTask, imgbbClient *imgbb.Client, progress *UploadProgress, stats *UploadStats) {
	// 读取分片文件
	chunkData, err := os.ReadFile(task.FilePath)
	if err != nil {
		atomic.AddInt64(&stats.FailureCount, 1)
		return
	}

	// 超级智能重试机制 - 支持API密钥切换！
	maxRetries := 10
	baseDelay := 2 * time.Second
	currentAPIKeyIndex := workerID % len(apiKeys) // 当前使用的密钥索引

	for retry := 0; retry < maxRetries; retry++ {
		uploadResult, err := imgbbClient.UploadWithSteganography(chunkData, task.ChunkName)

		if err == nil && uploadResult.Success {
			// 上传成功
			progress.mutex.Lock()
			progress.ChunkURLs[task.ChunkName] = uploadResult.URL
			progress.LastUpdated = time.Now()
			progress.mutex.Unlock()

			atomic.AddInt64(&progress.UploadedChunks, 1)
			atomic.AddInt64(&stats.SuccessCount, 1)

			fmt.Printf("✅ Worker-%d: 分片 %d 上传成功 (重试%d次)\n", workerID, task.Index+1, retry)
			return
		}

		// 分析错误类型
		if err != nil {
			errorMsg := err.Error()
			if strings.Contains(errorMsg, "Rate limit") {
				atomic.AddInt64(&stats.RateLimitCount, 1)
				// 速率限制，增加延迟
				delay := baseDelay * time.Duration(retry+1) * 2
				fmt.Printf("⏳ Worker-%d: 速率限制，等待 %v 后重试 (%d/%d)...\n", workerID, delay, retry+1, maxRetries)
				time.Sleep(delay)
				continue
			} else if strings.Contains(errorMsg, "Invalid API") {
				atomic.AddInt64(&stats.APIErrorCount, 1)

				// API密钥错误，尝试切换到下一个密钥
				if retry >= 2 { // 重试2次后切换密钥
					currentAPIKeyIndex = (currentAPIKeyIndex + 1) % len(apiKeys)
					newAPIKey := apiKeys[currentAPIKeyIndex]

					// 创建新的ImgBB客户端
					imgbbConfig := &imgbb.Config{
						APIKey: newAPIKey,
					}
					imgbbClient = imgbb.NewClient(imgbbConfig)

					fmt.Printf("🔄 Worker-%d: 切换到新API密钥: %s...%s (%d/%d)\n",
						workerID, newAPIKey[:8], newAPIKey[len(newAPIKey)-8:], retry+1, maxRetries)
				} else {
					delay := baseDelay * time.Duration(retry+1) * 2
					fmt.Printf("🔑 Worker-%d: API密钥问题，等待 %v 后重试 (%d/%d)...\n", workerID, delay, retry+1, maxRetries)
					time.Sleep(delay)
				}
				continue
			}
		}

		if !uploadResult.Success {
			fmt.Printf("❌ Worker-%d: 分片 %d 上传失败: %s，重试 (%d/%d)\n", workerID, task.Index+1, uploadResult.Error, retry+1, maxRetries)
		}

		// 普通错误，短暂延迟后重试
		if retry < maxRetries-1 {
			delay := baseDelay * time.Duration(retry+1)
			time.Sleep(delay)
		}
	}

	atomic.AddInt64(&stats.FailureCount, 1)
	fmt.Printf("💥 Worker-%d: 分片 %d 最终失败\n", workerID, task.Index+1)
}

// scanChunkFiles 扫描分片文件
func scanChunkFiles(encryptedDir string) ([]string, error) {
	var chunkFiles []string
	entries, err := os.ReadDir(encryptedDir)
	if err != nil {
		return nil, err
	}
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".enc") {
			chunkFiles = append(chunkFiles, filepath.Join(encryptedDir, entry.Name()))
		}
	}
	sort.Strings(chunkFiles)
	return chunkFiles, nil
}

// loadProgress 加载上传进度
func loadProgress(progressFile string, totalChunks int) *UploadProgress {
	progress := &UploadProgress{
		TotalChunks:    totalChunks,
		UploadedChunks: 0,
		ChunkURLs:      make(map[string]string),
		LastUpdated:    time.Now(),
	}
	if data, err := os.ReadFile(progressFile); err == nil {
		json.Unmarshal(data, progress)
		progress.TotalChunks = totalChunks
		atomic.StoreInt64(&progress.UploadedChunks, int64(len(progress.ChunkURLs)))
	}
	return progress
}

// saveProgress 保存上传进度
func saveProgress(progressFile string, progress *UploadProgress) {
	progress.mutex.RLock()
	defer progress.mutex.RUnlock()
	data, _ := json.MarshalIndent(progress, "", "  ")
	os.WriteFile(progressFile, data, 0644)
}
