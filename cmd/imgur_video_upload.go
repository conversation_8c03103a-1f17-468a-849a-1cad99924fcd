package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgur"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"
)

func main() {
	fmt.Println("🎬 Imgur视频上传工具 - 完整MixFile流程")
	fmt.Println(strings.Repeat("=", 60))

	// 目标视频文件
	videoFile := "/www/wwwroot/JAVAPI.COM/downloads/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合.mp4"

	// 检查文件是否存在
	if _, err := os.Stat(videoFile); os.IsNotExist(err) {
		fmt.Printf("❌ 视频文件不存在: %s\n", videoFile)
		return
	}

	// 获取文件信息
	fileInfo, err := os.Stat(videoFile)
	if err != nil {
		fmt.Printf("❌ 获取文件信息失败: %v\n", err)
		return
	}

	fmt.Printf("📁 目标文件: %s\n", filepath.Base(videoFile))
	fmt.Printf("📏 文件大小: %.2f MB\n", float64(fileInfo.Size())/1024/1024)

	// 初始化日志
	logger.Init("info", "text")

	// 步骤1：文件处理（分片和加密）
	fmt.Println("\n🔪 步骤1: 文件分片和加密...")
	workDir := fmt.Sprintf("/tmp/imgur_upload_%d", time.Now().Unix())

	config := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: true,
		KeepOriginal:      true,
		WorkDir:           workDir,
	}

	processor := fileprocessor.NewProcessor(config)

	result, err := processor.ProcessFile(videoFile, func(stage string, progress float64, message string) {
		if int(progress)%10 == 0 { // 只显示每10%的进度
			fmt.Printf("  📊 %s: %.0f%%\n", stage, progress)
		}
	})

	if err != nil {
		fmt.Printf("❌ 文件处理失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 文件处理完成: %d个分片\n", result.ChunkCount)
	fmt.Printf("🔐 加密密钥: %s\n", result.EncryptionKey)

	// 步骤2：配置Imgur客户端
	fmt.Println("\n☁️ 步骤2: 配置Imgur客户端...")

	// Imgur应用信息 - 使用您注册的实际Client ID
	imgurConfig := &imgur.Config{
		ClientID:     "c9a7f5307f449e0", // 您的实际Client ID
		ClientSecret: "",                // 暂时为空，使用Client-ID认证
		BaseURL:      "https://api.imgur.com/3",
		Timeout:      30 * time.Second,
		MaxRetries:   3,
	}

	imgurClient := imgur.NewClient(imgurConfig)

	// 测试连接
	if err := imgurClient.TestConnection(); err != nil {
		fmt.Printf("❌ Imgur连接测试失败: %v\n", err)
		fmt.Println("💡 请确保您的Client ID配置正确")
		return
	}
	fmt.Println("✅ Imgur连接测试成功")

	// 步骤3：上传分片到Imgur
	fmt.Println("\n📤 步骤3: 上传分片到Imgur...")

	// 获取加密分片路径
	encryptedDir := filepath.Join(result.WorkDir, "encrypted")
	chunkFiles, err := scanChunkFiles(encryptedDir)
	if err != nil {
		fmt.Printf("❌ 扫描分片文件失败: %v\n", err)
		return
	}

	fmt.Printf("📦 找到 %d 个加密分片\n", len(chunkFiles))

	// 上传分片
	var uploadResults []imgur.UploadResult
	for i, chunkFile := range chunkFiles {
		// 读取分片数据
		chunkData, err := os.ReadFile(chunkFile)
		if err != nil {
			fmt.Printf("❌ 读取分片文件失败: %v\n", err)
			continue
		}

		// 上传分片（使用隐写术）
		chunkName := filepath.Base(chunkFile)
		uploadResult, err := imgurClient.UploadWithSteganography(chunkData, chunkName)
		if err != nil {
			fmt.Printf("❌ 上传分片 %s 失败: %v\n", chunkName, err)
			continue
		}

		if !uploadResult.Success {
			fmt.Printf("❌ 上传分片 %s 失败: %s\n", chunkName, uploadResult.Error)
			continue
		}

		uploadResults = append(uploadResults, *uploadResult)
		fmt.Printf("  ✅ 分片 %d/%d 上传成功\n", i+1, len(chunkFiles))

		// 避免API限制
		time.Sleep(2 * time.Second)
	}

	fmt.Printf("✅ 上传完成: %d/%d 成功\n", len(uploadResults), len(chunkFiles))

	if len(uploadResults) == 0 {
		fmt.Println("❌ 没有成功上传的分片，无法生成分享码")
		return
	}

	// 步骤4：生成MixFile分享码
	fmt.Println("\n🔗 步骤4: 生成MixFile分享码...")

	shareCode, playURL, err := generateMixFileShareCode(result, uploadResults)
	if err != nil {
		fmt.Printf("❌ 生成分享码失败: %v\n", err)
		return
	}

	// 输出结果
	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Printf("🎉 Imgur上传完成！\n")
	fmt.Printf("📊 统计信息:\n")
	fmt.Printf("  📁 原文件: %s (%.2f MB)\n", filepath.Base(videoFile), float64(fileInfo.Size())/1024/1024)
	fmt.Printf("  📦 分片数量: %d\n", result.ChunkCount)
	fmt.Printf("  ☁️ 上传成功: %d\n", len(uploadResults))
	fmt.Printf("  🔐 加密密钥: %s\n", result.EncryptionKey)
	fmt.Printf("\n🔗 分享信息:\n")
	fmt.Printf("  📋 分享码: %s\n", shareCode)
	fmt.Printf("  🎬 播放链接: %s\n", playURL)
	fmt.Printf("\n💡 使用说明:\n")
	fmt.Printf("  1. 复制分享码到支持MixFile的播放器\n")
	fmt.Printf("  2. 或直接使用播放链接在浏览器中播放\n")
	fmt.Printf("  3. 支持流式播放，无需等待完整下载\n")
	fmt.Printf("  4. 所有数据已通过隐写术安全存储在Imgur\n")
}

// scanChunkFiles 扫描分片文件
func scanChunkFiles(encryptedDir string) ([]string, error) {
	var chunkFiles []string
	entries, err := os.ReadDir(encryptedDir)
	if err != nil {
		return nil, err
	}

	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".enc") {
			chunkFiles = append(chunkFiles, filepath.Join(encryptedDir, entry.Name()))
		}
	}

	return chunkFiles, nil
}

// generateMixFileShareCode 生成MixFile分享码
func generateMixFileShareCode(result *fileprocessor.ProcessingResult, uploadResults []imgur.UploadResult) (string, string, error) {
	// 创建分片信息
	chunks := make([]mixfile.ChunkInfo, len(uploadResults))
	for i, uploadResult := range uploadResults {
		chunks[i] = mixfile.ChunkInfo{
			Index: i,
			URL:   uploadResult.URL,
			Size:  int64(uploadResult.Size),
			Hash:  "", // 可以后续添加
		}
	}

	// 创建索引文件
	index := mixfile.NewIndexFile("video.mp4", result.TotalSize, 1024*1024)
	index.EncryptionKey = result.EncryptionKey
	index.OriginalFileHash = result.OriginalFileHash

	// 添加所有分片
	for _, chunk := range chunks {
		index.AddChunk(chunk)
	}

	// 序列化索引
	indexManager := mixfile.NewIndexManager()
	indexData, err := indexManager.SerializeIndex(index)
	if err != nil {
		return "", "", fmt.Errorf("序列化索引失败: %w", err)
	}

	// 压缩索引
	compressedIndex, err := indexManager.CompressIndex(indexData)
	if err != nil {
		return "", "", fmt.Errorf("压缩索引失败: %w", err)
	}

	// 加密索引
	encryptionKey := []byte("0123456789abcdef0123456789abcdef") // 32字节密钥
	encryptedIndex, err := indexManager.EncryptIndex(compressedIndex, encryptionKey)
	if err != nil {
		return "", "", fmt.Errorf("加密索引失败: %w", err)
	}

	// 上传索引文件到Imgur
	imgurConfig := &imgur.Config{
		ClientID:   "c9a7f5307f449e0", // 您的实际Client ID
		BaseURL:    "https://api.imgur.com/3",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
	}
	imgurClient := imgur.NewClient(imgurConfig)

	indexUploadResult, err := imgurClient.UploadWithSteganography(encryptedIndex, "index.json")
	if err != nil {
		return "", "", fmt.Errorf("上传索引失败: %w", err)
	}

	if !indexUploadResult.Success {
		return "", "", fmt.Errorf("索引上传失败: %s", indexUploadResult.Error)
	}

	// 生成分享码
	shareCode := fmt.Sprintf("mixfile://%s", indexUploadResult.URL)
	playURL := fmt.Sprintf("https://mixfile-player.example.com/play?code=%s", indexUploadResult.URL)

	return shareCode, playURL, nil
}
