package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
)

func main() {
	fmt.Println("🧪 测试imgbb API上传功能...")
	fmt.Println(strings.Repeat("=", 60))

	// 初始化日志
	logger.Init("info", "text")

	// 创建imgbb客户端配置
	config := &imgbb.Config{
		APIKey:     "eb1bd40c679abd8b7210f86cc31586e7",
		BaseURL:    "https://api.imgbb.com/1",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
	}

	client := imgbb.NewClient(config)

	// 测试1：连接测试
	fmt.Println("📡 测试1: API连接测试...")
	if err := client.TestConnection(); err != nil {
		log.Fatalf("❌ API连接测试失败: %v", err)
	}
	fmt.Println("✅ API连接测试成功！")

	// 测试2：创建测试文件并上传
	fmt.Println("\n📤 测试2: 文件上传测试...")
	
	// 创建一个测试图片文件（简单的PNG）
	testImageData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
		0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x10,
		0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x91, 0x68, 0x36, 0x00, 0x00, 0x00,
		0x3A, 0x49, 0x44, 0x41, 0x54, 0x28, 0x15, 0x63, 0xF8, 0xCF, 0xC0, 0xC0,
		0xC0, 0x00, 0x04, 0x20, 0x80, 0x18, 0x30, 0x8A, 0x81, 0x81, 0x01, 0x02,
		0x08, 0x20, 0x06, 0x8C, 0x62, 0x60, 0x60, 0x80, 0x00, 0x02, 0x88, 0x01,
		0xA3, 0x18, 0x18, 0x18, 0x20, 0x80, 0x00, 0x62, 0xC0, 0x28, 0x06, 0x06,
		0x06, 0x08, 0x20, 0x80, 0x18, 0x30, 0x8A, 0x81, 0x81, 0x01, 0x02, 0x08,
		0x00, 0x00, 0x84, 0x3E, 0x0C, 0x8F, 0x0A, 0x72, 0xF8, 0x8F, 0x00, 0x00,
		0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
	}

	// 保存测试文件
	testFile := "/tmp/test_upload.png"
	if err := os.WriteFile(testFile, testImageData, 0644); err != nil {
		log.Fatalf("❌ 创建测试文件失败: %v", err)
	}
	defer os.Remove(testFile)

	fmt.Printf("📁 创建测试文件: %s (大小: %d bytes)\n", testFile, len(testImageData))

	// 上传测试文件
	result, err := client.UploadFile(testFile)
	if err != nil {
		log.Fatalf("❌ 文件上传失败: %v", err)
	}

	if !result.Success {
		log.Fatalf("❌ 上传失败: %s", result.Error)
	}

	fmt.Printf("✅ 文件上传成功！\n")
	fmt.Printf("   📷 图片URL: %s\n", result.URL)
	fmt.Printf("   🗑️ 删除URL: %s\n", result.DeleteURL)
	fmt.Printf("   📏 文件大小: %d bytes\n", result.Size)

	// 测试3：批量上传测试
	fmt.Println("\n📦 测试3: 批量上传测试...")

	// 创建多个测试文件
	testFiles := []string{}
	uploadJobs := []imgbb.UploadJob{}

	for i := 0; i < 3; i++ {
		filename := fmt.Sprintf("/tmp/test_batch_%d.png", i)
		if err := os.WriteFile(filename, testImageData, 0644); err != nil {
			log.Fatalf("❌ 创建批量测试文件失败: %v", err)
		}
		testFiles = append(testFiles, filename)

		uploadJobs = append(uploadJobs, imgbb.UploadJob{
			Index:    i,
			FilePath: filename,
			Filename: fmt.Sprintf("test_batch_%d.png", i),
		})
	}

	// 清理测试文件
	defer func() {
		for _, file := range testFiles {
			os.Remove(file)
		}
	}()

	fmt.Printf("📁 创建 %d 个批量测试文件\n", len(testFiles))

	// 执行批量上传
	batchResult, err := client.BatchUpload(uploadJobs, 2, func(completed, total int) {
		fmt.Printf("   📊 上传进度: %d/%d (%.1f%%)\n", completed, total, float64(completed)/float64(total)*100)
	})

	if err != nil {
		log.Fatalf("❌ 批量上传失败: %v", err)
	}

	fmt.Printf("✅ 批量上传完成！\n")
	fmt.Printf("   📊 总数: %d, 成功: %d, 失败: %d\n", batchResult.Total, batchResult.Success, batchResult.Failed)
	fmt.Printf("   ⏱️ 耗时: %v\n", batchResult.Duration)

	if batchResult.Success > 0 {
		fmt.Printf("   🔗 上传的URL:\n")
		for i, url := range batchResult.URLs {
			fmt.Printf("     %d. %s\n", i+1, url)
		}
	}

	if batchResult.Failed > 0 {
		fmt.Printf("   ❌ 错误信息:\n")
		for i, errMsg := range batchResult.Errors {
			fmt.Printf("     %d. %s\n", i+1, errMsg)
		}
	}

	// 测试4：模拟分片文件上传
	fmt.Println("\n🧩 测试4: 模拟分片文件上传...")

	// 创建模拟的加密分片文件
	chunkData := make([]byte, 1024*1024) // 1MB
	for i := range chunkData {
		chunkData[i] = byte(i % 256)
	}

	chunkFile := "/tmp/test_chunk_001.enc"
	if err := os.WriteFile(chunkFile, chunkData, 0644); err != nil {
		log.Fatalf("❌ 创建分片文件失败: %v", err)
	}
	defer os.Remove(chunkFile)

	fmt.Printf("📁 创建模拟分片文件: %s (大小: %.2f MB)\n", chunkFile, float64(len(chunkData))/1024/1024)

	// 上传分片文件
	chunkResult, err := client.UploadFile(chunkFile)
	if err != nil {
		log.Fatalf("❌ 分片文件上传失败: %v", err)
	}

	if !chunkResult.Success {
		log.Fatalf("❌ 分片上传失败: %s", chunkResult.Error)
	}

	fmt.Printf("✅ 分片文件上传成功！\n")
	fmt.Printf("   📷 分片URL: %s\n", chunkResult.URL)
	fmt.Printf("   📏 文件大小: %d bytes\n", chunkResult.Size)

	// 输出总结
	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Printf("🎉 imgbb API测试完成！\n")
	fmt.Printf("📊 测试结果:\n")
	fmt.Printf("  ✅ API连接: 正常\n")
	fmt.Printf("  ✅ 单文件上传: 正常\n")
	fmt.Printf("  ✅ 批量上传: 正常 (%d/%d 成功)\n", batchResult.Success, batchResult.Total)
	fmt.Printf("  ✅ 分片上传: 正常\n")
	fmt.Printf("\n💡 API配置正确，可以用于生产环境！\n")
	fmt.Printf("🔑 API密钥: %s...%s\n", config.APIKey[:8], config.APIKey[len(config.APIKey)-8:])
}