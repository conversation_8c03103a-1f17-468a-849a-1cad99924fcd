package imgur

import (
	"bytes"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/steganography"
)

// Config Imgur配置
type Config struct {
	ClientID     string        `json:"client_id"`     // 客户端ID
	ClientSecret string        `json:"client_secret"` // 客户端密钥
	AccessToken  string        `json:"access_token"`  // 访问令牌
	RefreshToken string        `json:"refresh_token"` // 刷新令牌
	BaseURL      string        `json:"base_url"`      // API基础URL
	Timeout      time.Duration `json:"timeout"`       // 请求超时时间
	MaxRetries   int           `json:"max_retries"`   // 最大重试次数
}

// UploadResponse Imgur上传响应
type UploadResponse struct {
	Data    *ImageData `json:"data"`
	Success bool       `json:"success"`
	Status  int        `json:"status"`
}

// ImageData 图片数据
type ImageData struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Datetime    int64  `json:"datetime"`
	Type        string `json:"type"`
	Animated    bool   `json:"animated"`
	Width       int    `json:"width"`
	Height      int    `json:"height"`
	Size        int    `json:"size"`
	Views       int    `json:"views"`
	Bandwidth   int64  `json:"bandwidth"`
	Vote        string `json:"vote"`
	Favorite    bool   `json:"favorite"`
	Nsfw        bool   `json:"nsfw"`
	Section     string `json:"section"`
	AccountURL  string `json:"account_url"`
	AccountID   int    `json:"account_id"`
	IsAd        bool   `json:"is_ad"`
	InMostViral bool   `json:"in_most_viral"`
	HasSound    bool   `json:"has_sound"`
	Tags        []Tag  `json:"tags"`
	AdType      int    `json:"ad_type"`
	AdURL       string `json:"ad_url"`
	Edited      string `json:"edited"`
	InGallery   bool   `json:"in_gallery"`
	DeleteHash  string `json:"deletehash"`
	Name        string `json:"name"`
	Link        string `json:"link"`
}

// Tag 标签
type Tag struct {
	Name                 string `json:"name"`
	DisplayName          string `json:"display_name"`
	Followers            int    `json:"followers"`
	TotalItems           int    `json:"total_items"`
	Following            bool   `json:"following"`
	IsWhitelisted        bool   `json:"is_whitelisted"`
	BackgroundHash       string `json:"background_hash"`
	ThumbnailHash        string `json:"thumbnail_hash"`
	Accent               string `json:"accent"`
	BackgroundIsAnimated bool   `json:"background_is_animated"`
	ThumbnailIsAnimated  bool   `json:"thumbnail_is_animated"`
	IsPromoted           bool   `json:"is_promoted"`
	Description          string `json:"description"`
	LogoHash             string `json:"logo_hash"`
	LogoDestinationURL   string `json:"logo_destination_url"`
}

// UploadResult 上传结果（兼容ImgBB接口）
type UploadResult struct {
	Success   bool   `json:"success"`
	URL       string `json:"url"`
	DeleteURL string `json:"delete_url"`
	Size      int    `json:"size"`
	Error     string `json:"error,omitempty"`
}

// TokenResponse OAuth2令牌响应
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
	Scope        string `json:"scope"`
}

// Client Imgur客户端
type Client struct {
	config     *Config
	httpClient *http.Client
	mutex      sync.RWMutex
}

// NewClient 创建新的Imgur客户端
func NewClient(config *Config) *Client {
	if config == nil {
		config = &Config{
			BaseURL:    "https://api.imgur.com/3",
			Timeout:    30 * time.Second,
			MaxRetries: 3,
		}
	}

	if config.BaseURL == "" {
		config.BaseURL = "https://api.imgur.com/3"
	}

	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// SetAccessToken 设置访问令牌
func (c *Client) SetAccessToken(accessToken string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.config.AccessToken = accessToken
}

// GetAccessToken 获取访问令牌
func (c *Client) GetAccessToken() string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.config.AccessToken
}

// UploadFile 上传文件
func (c *Client) UploadFile(filePath string) (*UploadResult, error) {
	// 读取文件
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	filename := filepath.Base(filePath)
	return c.UploadData(fileData, filename)
}

// UploadData 上传数据
func (c *Client) UploadData(data []byte, filename string) (*UploadResult, error) {
	if c.config.ClientID == "" && c.config.AccessToken == "" {
		return &UploadResult{
			Success: false,
			Error:   "Client ID or Access Token is required",
		}, fmt.Errorf("client ID or access token is required")
	}

	// 检查是否为图片文件，如果不是则伪装成PNG
	processedData, processedFilename := c.processDataForUpload(data, filename)

	// 将数据编码为base64
	base64Data := base64.StdEncoding.EncodeToString(processedData)

	// 创建表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加图片数据
	if err := writer.WriteField("image", base64Data); err != nil {
		return nil, fmt.Errorf("failed to write image field: %w", err)
	}

	// 添加类型
	if err := writer.WriteField("type", "base64"); err != nil {
		return nil, fmt.Errorf("failed to write type field: %w", err)
	}

	// 添加标题
	if err := writer.WriteField("title", processedFilename); err != nil {
		return nil, fmt.Errorf("failed to write title field: %w", err)
	}

	writer.Close()

	// 执行上传请求
	var lastErr error
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避
			backoff := time.Duration(attempt*attempt) * time.Second
			logger.Debugf("Retrying upload after %v (attempt %d/%d)", backoff, attempt+1, c.config.MaxRetries)
			time.Sleep(backoff)
		}

		result, err := c.doUpload(&buf, writer.FormDataContentType(), filename)
		if err == nil {
			return result, nil
		}

		lastErr = err
		logger.Warnf("Upload attempt %d failed: %v", attempt+1, err)
	}

	return &UploadResult{
		Success: false,
		Error:   fmt.Sprintf("upload failed after %d attempts: %v", c.config.MaxRetries, lastErr),
	}, lastErr
}

// doUpload 执行上传请求
func (c *Client) doUpload(body *bytes.Buffer, contentType, filename string) (*UploadResult, error) {
	url := fmt.Sprintf("%s/image", c.config.BaseURL)

	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", contentType)
	req.Header.Set("User-Agent", "magnet-downloader/1.0")

	// 设置认证头
	if c.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+c.config.AccessToken)
	} else if c.config.ClientID != "" {
		req.Header.Set("Authorization", "Client-ID "+c.config.ClientID)
	}

	logger.Debugf("Uploading to Imgur: filename=%s, size=%d", filename, body.Len())

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 调试：打印响应内容
	logger.Debugf("Imgur response status: %d", resp.StatusCode)
	logger.Debugf("Imgur response body: %s", string(respBody))

	// 解析响应
	var uploadResp UploadResponse
	if err := json.Unmarshal(respBody, &uploadResp); err != nil {
		return nil, fmt.Errorf("failed to parse response (status: %d, body: %s): %w", resp.StatusCode, string(respBody), err)
	}

	// 检查响应状态
	if !uploadResp.Success {
		errorMsg := fmt.Sprintf("upload failed with status %d", uploadResp.Status)
		return &UploadResult{
			Success: false,
			Error:   errorMsg,
		}, fmt.Errorf(errorMsg)
	}

	logger.Infof("Upload successful: filename=%s, url=%s, size=%d",
		filename, uploadResp.Data.Link, uploadResp.Data.Size)

	return &UploadResult{
		Success:   true,
		URL:       uploadResp.Data.Link,
		DeleteURL: fmt.Sprintf("https://imgur.com/delete/%s", uploadResp.Data.DeleteHash),
		Size:      uploadResp.Data.Size,
	}, nil
}

// UploadWithSteganography 强制使用隐写术上传数据
func (c *Client) UploadWithSteganography(data []byte, filename string) (*UploadResult, error) {
	logger.Debugf("Force applying steganography: %s (%d bytes)", filename, len(data))

	// 强制使用隐写术
	config := steganography.DefaultConfig()
	steganographer := steganography.NewSteganographer(config)

	pngData, err := steganographer.HideDataInPNG(data)
	if err != nil {
		return nil, fmt.Errorf("failed to hide data in PNG: %w", err)
	}

	// 生成PNG文件名
	pngFilename := c.generatePNGFilename(filename)

	// 上传PNG数据
	return c.UploadData(pngData, pngFilename)
}

// TestConnection 测试连接
func (c *Client) TestConnection() error {
	url := fmt.Sprintf("%s/credits", c.config.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置认证头
	if c.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+c.config.AccessToken)
	} else if c.config.ClientID != "" {
		req.Header.Set("Authorization", "Client-ID "+c.config.ClientID)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	return nil
}

// ExchangeCodeForToken 交换授权码获取访问令牌
func (c *Client) ExchangeCodeForToken(code string) (*TokenResponse, error) {
	url := "https://api.imgur.com/oauth2/token"

	data := fmt.Sprintf("client_id=%s&client_secret=%s&grant_type=authorization_code&code=%s",
		c.config.ClientID, c.config.ClientSecret, code)

	req, err := http.NewRequest("POST", url, strings.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token exchange failed with status %d", resp.StatusCode)
	}

	var tokenResp TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// 更新客户端的访问令牌
	c.SetAccessToken(tokenResp.AccessToken)

	return &tokenResp, nil
}

// processDataForUpload 处理上传数据，将非图片文件隐藏到PNG中
func (c *Client) processDataForUpload(data []byte, filename string) ([]byte, string) {
	// 检查是否为图片文件
	if c.isImageFile(filename) {
		logger.Debugf("File is already an image, uploading directly: %s", filename)
		return data, filename
	}

	logger.Debugf("Non-image file detected, applying steganography: %s (%d bytes)", filename, len(data))

	// 使用隐写术将数据隐藏到PNG中
	config := steganography.DefaultConfig()
	steganographer := steganography.NewSteganographer(config)

	pngData, err := steganographer.HideDataInPNG(data)
	if err != nil {
		logger.Errorf("Failed to hide data in PNG: %v", err)
		// 如果隐写失败，返回原始数据
		return data, filename
	}

	// 生成PNG文件名
	pngFilename := c.generatePNGFilename(filename)

	logger.Debugf("Steganography applied successfully: %s -> %s (%d -> %d bytes)",
		filename, pngFilename, len(data), len(pngData))

	return pngData, pngFilename
}

// isImageFile 检查是否为图片文件
func (c *Client) isImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}

	for _, imageExt := range imageExts {
		if ext == imageExt {
			return true
		}
	}

	return false
}

// generatePNGFilename 生成PNG文件名
func (c *Client) generatePNGFilename(originalFilename string) string {
	// 移除原始扩展名
	nameWithoutExt := strings.TrimSuffix(originalFilename, filepath.Ext(originalFilename))

	// 生成随机后缀
	randomSuffix := c.generateRandomString(8)

	return fmt.Sprintf("%s_%s.png", nameWithoutExt, randomSuffix)
}

// generateRandomString 生成随机字符串
func (c *Client) generateRandomString(length int) string {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳
		return fmt.Sprintf("%x", time.Now().UnixNano())[:length]
	}
	return hex.EncodeToString(bytes)[:length]
}
