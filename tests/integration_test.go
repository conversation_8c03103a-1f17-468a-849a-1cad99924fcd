package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"magnet-downloader/internal/api"
	"magnet-downloader/internal/config"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMagnetDownloadAndProcessingIntegration 磁力下载和文件处理集成测试
func TestMagnetDownloadAndProcessingIntegration(t *testing.T) {
	// 跳过集成测试，除非明确指定
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// 设置测试环境
	gin.SetMode(gin.TestMode)

	// 加载测试配置
	cfg, err := config.Load()
	require.NoError(t, err, "Failed to load config")

	// 初始化数据库
	err = database.Init(&cfg.Database)
	require.NoError(t, err, "Failed to initialize database")

	// 创建服务
	repo := repository.NewRepository(database.GetDB())
	services := service.NewServices(repo, cfg)

	// 创建路由
	router := api.NewRouter(cfg, services)
	router.Setup()

	// 创建测试服务器
	server := httptest.NewServer(router.GetEngine())
	defer server.Close()

	t.Run("Complete Workflow Test", func(t *testing.T) {
		// 1. 用户登录
		token := loginTestUser(t, server.URL)

		// 2. 创建磁力下载任务
		taskID := createDownloadTask(t, server.URL, token)

		// 3. 启动下载任务
		startDownloadTask(t, server.URL, token, taskID)

		// 4. 等待下载完成（模拟）
		waitForDownloadCompletion(t, server.URL, token, taskID)

		// 5. 验证文件处理自动启动
		verifyProcessingStarted(t, server.URL, token, taskID)

		// 6. 监控处理进度
		monitorProcessingProgress(t, server.URL, token, taskID)

		// 7. 验证播放列表生成
		verifyPlaylistGeneration(t, server.URL, token, taskID)

		// 8. 测试API接口
		testProcessingAPIs(t, server.URL, token, taskID)
	})
}

// loginTestUser 登录测试用户
func loginTestUser(t *testing.T, baseURL string) string {
	loginData := map[string]string{
		"username": "admin",
		"password": "admin123",
	}

	jsonData, _ := json.Marshal(loginData)
	resp, err := http.Post(baseURL+"/api/v1/auth/login", "application/json", bytes.NewBuffer(jsonData))
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err)

	data := result["data"].(map[string]interface{})
	return data["token"].(string)
}

// createDownloadTask 创建下载任务
func createDownloadTask(t *testing.T, baseURL, token string) uint {
	taskData := map[string]interface{}{
		"magnet_uri": "magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5",
		"task_name":  "Test Task",
		"save_path":  "/tmp/downloads",
	}

	jsonData, _ := json.Marshal(taskData)
	req, _ := http.NewRequest("POST", baseURL+"/api/v1/tasks", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusCreated, resp.StatusCode)

	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err)

	data := result["data"].(map[string]interface{})
	return uint(data["id"].(float64))
}

// startDownloadTask 启动下载任务
func startDownloadTask(t *testing.T, baseURL, token string, taskID uint) {
	req, _ := http.NewRequest("POST", fmt.Sprintf("%s/api/v1/tasks/%d/start", baseURL, taskID), nil)
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)
}

// waitForDownloadCompletion 等待下载完成
func waitForDownloadCompletion(t *testing.T, baseURL, token string, taskID uint) {
	// 在实际测试中，这里会轮询任务状态
	// 为了测试目的，我们模拟等待
	time.Sleep(2 * time.Second)

	t.Logf("Simulating download completion for task %d", taskID)
}

// verifyProcessingStarted 验证处理已启动
func verifyProcessingStarted(t *testing.T, baseURL, token string, taskID uint) {
	req, _ := http.NewRequest("GET", fmt.Sprintf("%s/api/v1/processing/%d/status", baseURL, taskID), nil)
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	// 处理可能还未开始，所以不强制要求成功
	t.Logf("Processing status check response: %d", resp.StatusCode)
}

// monitorProcessingProgress 监控处理进度
func monitorProcessingProgress(t *testing.T, baseURL, token string, taskID uint) {
	req, _ := http.NewRequest("GET", fmt.Sprintf("%s/api/v1/processing/%d/progress", baseURL, taskID), nil)
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	t.Logf("Processing progress check response: %d", resp.StatusCode)
}

// verifyPlaylistGeneration 验证播放列表生成
func verifyPlaylistGeneration(t *testing.T, baseURL, token string, taskID uint) {
	req, _ := http.NewRequest("GET", fmt.Sprintf("%s/api/v1/processing/%d/playlist", baseURL, taskID), nil)
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	t.Logf("Playlist check response: %d", resp.StatusCode)
}

// testProcessingAPIs 测试处理API
func testProcessingAPIs(t *testing.T, baseURL, token string, taskID uint) {
	client := &http.Client{}

	// 测试统计接口
	req, _ := http.NewRequest("GET", baseURL+"/api/v1/processing/stats", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	resp, err := client.Do(req)
	require.NoError(t, err)
	resp.Body.Close()
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	// 测试任务列表接口
	req, _ = http.NewRequest("GET", baseURL+"/api/v1/processing/tasks", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	resp, err = client.Do(req)
	require.NoError(t, err)
	resp.Body.Close()
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	t.Log("All processing APIs tested successfully")
}

// TestConfigValidation 配置验证测试
func TestConfigValidation(t *testing.T) {
	t.Run("Valid Config", func(t *testing.T) {
		cfg := &config.Config{
			App: config.AppConfig{
				Name:    "test-app",
				Version: "1.0.0",
				Env:     "test",
			},
			Server: config.ServerConfig{
				Host: "localhost",
				Port: 8080,
				Mode: "debug",
			},
			Database: config.DatabaseConfig{
				Host:   "localhost",
				Port:   5432,
				DBName: "test_db",
			},
			Redis: config.RedisConfig{
				Host: "localhost",
				Port: 6379,
			},
			Aria2: config.Aria2Config{
				Host: "localhost",
				Port: 6800,
			},
			JWT: config.JWTConfig{
				Secret:     "test-secret-key",
				ExpireTime: 3600,
			},
			FileProcessing: config.FileProcessingConfig{
				Enabled:              true,
				ChunkSizeMB:          1,
				MaxConcurrentUploads: 3,
				EncryptionAlgorithm:  "aes-gcm-256",
				EncryptionEnabled:    true,
				WorkDir:              "/tmp/test",
				RetryAttempts:        3,
				CleanupAfterDays:     30,
				ImgBB: config.ImgBBConfig{
					APIKey:     "test-api-key",
					BaseURL:    "https://api.imgbb.com/1",
					Timeout:    30,
					MaxRetries: 3,
				},
				Playlist: config.PlaylistConfig{
					Version:        3,
					TargetDuration: 10,
					PlaylistType:   "VOD",
				},
			},
		}

		err := cfg.Validate()
		assert.NoError(t, err)
	})

	t.Run("Invalid Config", func(t *testing.T) {
		cfg := &config.Config{
			FileProcessing: config.FileProcessingConfig{
				Enabled:     true,
				ChunkSizeMB: 0, // 无效值
				ImgBB: config.ImgBBConfig{
					APIKey: "", // 缺少必需的API密钥
				},
			},
		}

		err := cfg.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "chunk_size_mb")
	})
}
